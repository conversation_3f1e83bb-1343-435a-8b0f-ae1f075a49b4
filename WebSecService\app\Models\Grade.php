<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Grade extends Model  {

    public function user(): BelongsTo {
        return $this->belongsTo(User::class);
    }

    public function course(): BelongsTo {
        return $this->belongsTo(Course::class);
    }

    public function appeals(): HasMany {
        return $this->hasMany(Appeal::class);
    }

    public function latestAppeal() {
        return $this->hasOne(Appeal::class)->latest();
    }

    protected $fillable = [
        'course_id',
        'user_id',
        'degree',
    ];

    /**
     * Check if this grade has any pending appeals.
     */
    public function hasPendingAppeal(): bool
    {
        return $this->appeals()->where('status', 'pending')->exists();
    }

    /**
     * Get the current appeal status for this grade.
     */
    public function getAppealStatus(): string
    {
        $latestAppeal = $this->latestAppeal;
        return $latestAppeal ? $latestAppeal->status : 'none';
    }
}
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create new permissions for exam grades
        $permissions = [
            ['name' => 'show_exgrades', 'display_name' => 'Show Exam Grades', 'guard_name' => 'web'],
            ['name' => 'edit_exgrades', 'display_name' => 'Edit Exam Grades', 'guard_name' => 'web'],
            ['name' => 'delete_exgrades', 'display_name' => 'Delete Exam Grades', 'guard_name' => 'web'],
        ];

        foreach ($permissions as $permission) {
            Permission::create($permission);
        }

        // Create new roles for exam system
        $roles = [
            ['name' => 'exmanager', 'guard_name' => 'web'],
            ['name' => 'exteacher', 'guard_name' => 'web'],
            ['name' => 'exstudent', 'guard_name' => 'web'],
        ];

        foreach ($roles as $role) {
            Role::create($role);
        }

        // Assign permissions to roles
        $exteacher = Role::findByName('exteacher');
        $exteacher->givePermissionTo(['show_exgrades', 'edit_exgrades', 'delete_exgrades']);

        $exmanager = Role::findByName('exmanager');
        $exmanager->givePermissionTo(['show_exgrades', 'delete_exgrades']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove role-permission assignments
        $exteacher = Role::findByName('exteacher');
        if ($exteacher) {
            $exteacher->revokePermissionTo(['show_exgrades', 'edit_exgrades', 'delete_exgrades']);
        }

        $exmanager = Role::findByName('exmanager');
        if ($exmanager) {
            $exmanager->revokePermissionTo(['show_exgrades', 'delete_exgrades']);
        }

        // Delete roles
        Role::where('name', 'exmanager')->delete();
        Role::where('name', 'exteacher')->delete();
        Role::where('name', 'exstudent')->delete();

        // Delete permissions
        Permission::where('name', 'show_exgrades')->delete();
        Permission::where('name', 'edit_exgrades')->delete();
        Permission::where('name', 'delete_exgrades')->delete();
    }
};

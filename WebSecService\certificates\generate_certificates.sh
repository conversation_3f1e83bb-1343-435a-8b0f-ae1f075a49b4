#!/bin/bash

# Create certificates directory structure
mkdir -p ca/private
mkdir -p ca/certs
mkdir -p website
mkdir -p user

# Set permissions
chmod 700 ca/private

# Generate Root CA private key
openssl genrsa -out ca/private/ca.key 4096

# Create Root CA certificate
openssl req -new -x509 -days 365 -key ca/private/ca.key -out ca/certs/ca.crt -subj "/C=EG/ST=Cairo/L=Cairo/O=Final Exam Root/OU=Certificate Authority/CN=Final Exam Root CA"

# Generate website private key
openssl genrsa -out website/website.key 2048

# Create website certificate signing request
openssl req -new -key website/website.key -out website/website.csr -subj "/C=EG/ST=Cairo/L=Cairo/O=Final Exam/OU=Web Services/CN=www.final-exam.com"

# Sign website certificate with Root CA (valid for 1 year)
openssl x509 -req -in website/website.csr -CA ca/certs/ca.crt -CAkey ca/private/ca.key -CAcreateserial -out website/website.crt -days 365

# Generate user private key
openssl genrsa -out user/user.key 2048

# Create user certificate signing request (replace YOUR_CODE with actual student code)
openssl req -new -key user/user.key -out user/user.csr -subj "/C=EG/ST=Cairo/L=Cairo/O=Final Exam/OU=Students/CN=<EMAIL>/emailAddress=<EMAIL>"

# Sign user certificate with Root CA (valid for 3 months)
openssl x509 -req -in user/user.csr -CA ca/certs/ca.crt -CAkey ca/private/ca.key -CAcreateserial -out user/user.crt -days 90

# Create PKCS#12 file for user certificate (for browser import)
openssl pkcs12 -export -out user/user.p12 -inkey user/user.key -in user/user.crt -certfile ca/certs/ca.crt -password pass:12345678

# Display certificate information
echo "=== Root CA Certificate ==="
openssl x509 -in ca/certs/ca.crt -text -noout | grep -A 2 "Subject:"
openssl x509 -in ca/certs/ca.crt -text -noout | grep -A 2 "Validity"

echo ""
echo "=== Website Certificate ==="
openssl x509 -in website/website.crt -text -noout | grep -A 2 "Subject:"
openssl x509 -in website/website.crt -text -noout | grep -A 2 "Validity"

echo ""
echo "=== User Certificate ==="
openssl x509 -in user/user.crt -text -noout | grep -A 2 "Subject:"
openssl x509 -in user/user.crt -text -noout | grep -A 2 "Validity"

echo ""
echo "Certificates generated successfully!"
echo "Root CA: ca/certs/ca.crt"
echo "Website Certificate: website/website.crt"
echo "User Certificate: user/user.crt"
echo "User PKCS#12: user/user.p12 (password: 12345678)"

# SSL Certificates for Final Exam

This directory contains scripts to generate SSL certificates as required for the Final Exam project.

## Requirements

- OpenSSL must be installed and available in your system PATH
- For Windows: You can download OpenSSL from https://slproweb.com/products/Win32OpenSSL.html
- For Linux/Mac: OpenSSL is usually pre-installed

## Generated Certificates

### 1. Root Certificate Authority (CA)
- **Name**: Final Exam Root
- **File**: `ca/certs/ca.crt`
- **Private Key**: `ca/private/ca.key`
- **Validity**: 1 year
- **Purpose**: Acts as the root certificate authority for signing other certificates

### 2. Website Certificate
- **Domain**: www.final-exam.com
- **File**: `website/website.crt`
- **Private Key**: `website/website.key`
- **Validity**: 1 year (365 days)
- **Purpose**: SSL certificate for the website domain

### 3. User Certificate
- **Email**: <EMAIL>
- **File**: `user/user.crt`
- **Private Key**: `user/user.key`
- **PKCS#12 File**: `user/user.p12`
- **Validity**: 3 months (90 days)
- **Purpose**: Client certificate for user authentication

## How to Generate Certificates

### On Windows:
```bash
cd certificates
generate_certificates.bat
```

### On Linux/Mac:
```bash
cd certificates
chmod +x generate_certificates.sh
./generate_certificates.sh
```

## Using the Certificates

### 1. Root CA Certificate
- Install `ca/certs/ca.crt` in your browser's trusted root certificate authorities
- This allows the browser to trust certificates signed by this CA

### 2. Website Certificate
- Use `website/website.crt` and `website/website.key` to configure HTTPS on your web server
- Add an entry to your hosts file: `127.0.0.1 www.final-exam.com`

### 3. User Certificate
- Import `user/user.p12` into your browser (password: 12345678)
- This enables client certificate authentication

## Customization

The user certificate has been customized for student code: <EMAIL>

## File Structure

```
certificates/
├── ca/
│   ├── private/
│   │   └── ca.key          # Root CA private key
│   └── certs/
│       └── ca.crt          # Root CA certificate
├── website/
│   ├── website.key         # Website private key
│   ├── website.csr         # Website certificate signing request
│   └── website.crt         # Website certificate
├── user/
│   ├── user.key           # User private key
│   ├── user.csr           # User certificate signing request
│   ├── user.crt           # User certificate
│   └── user.p12           # User certificate in PKCS#12 format
├── generate_certificates.sh   # Linux/Mac script
├── generate_certificates.bat  # Windows script
└── README.md              # This file
```

## Security Notes

- Keep private keys secure and never share them
- The generated certificates are for educational purposes only
- In production, use certificates from trusted Certificate Authorities
- The PKCS#12 password is set to "12345678" for simplicity

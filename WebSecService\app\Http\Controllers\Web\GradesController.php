<?php
namespace App\Http\Controllers\Web;

use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\Request;
use DB;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Grade;
use App\Models\User;
use App\Models\Appeal;

class GradesController extends Controller {

	use ValidatesRequests;

	public function __construct()
    {
    }

	public function list(Request $request) {

		// Check permissions
		if (!auth()->user()->hasPermissionTo('show_exgrades') && !auth()->user()->hasRole('exstudent')) {
			abort(403, 'Unauthorized to view grades');
		}

		$query = Grade::select("grades.*")->with(['user', 'course', 'latestAppeal']);
		$query->join('users', 'users.id', 'grades.user_id');
		$query->join('courses', 'courses.id', 'grades.course_id');

		// Students can only see their own grades
		if (auth()->user()->hasRole('exstudent') && !auth()->user()->hasPermissionTo('show_exgrades')) {
			$query->where('grades.user_id', auth()->id());
		}

		$query->when($request->keywords,
		fn($q)=> $q->where(function($subQuery) use($request){
			$subQuery->orWhere("users.name", "like", "%$request->keywords%");
			$subQuery->orWhere("courses.name", "like", "%$request->keywords%");
		}));

		$query->when($request->order_by,
		fn($q)=> $q->orderBy($request->order_by, $request->order_direction??"ASC"));

		$grades = $query->get();

		return view('grades.list', compact('grades'));
	}

	public function edit(Request $request, Grade $grade = null) {

		// Check edit permissions
		if (!auth()->user()->hasPermissionTo('edit_exgrades')) {
			abort(403, 'Unauthorized to edit grades');
		}

		$grade = $grade??new Grade();

		$users = User::select('id', 'name')->get();
		$courses = Course::select('id', 'name')->get();

		return view('grades.edit', compact('grade', 'courses', 'users'));
	}

	public function save(Request $request, Grade $grade = null) {

		// Check edit permissions
		if (!auth()->user()->hasPermissionTo('edit_exgrades')) {
			abort(403, 'Unauthorized to edit grades');
		}

		$this->validate($request, [
	        'user_id' => ['required', 'numeric', 'exists:users,id'],
	        'course_id' => ['required', 'numeric', 'exists:courses,id'],
	        'degree' => ['required', 'numeric', 'max:100']
	    ]);

		$isNewGrade = !$grade || !$grade->exists;
		$grade = $grade??new Grade();
		$oldDegree = $grade->degree;

		$grade->fill($request->all());
		$grade->save();

		// If grade was modified, close any pending appeals
		if (!$isNewGrade && $oldDegree != $grade->degree) {
			$grade->appeals()->where('status', 'pending')->update([
				'status' => 'closed',
				'resolved_at' => now(),
				'resolved_by' => auth()->id(),
				'resolution_notes' => 'Grade was modified by teacher'
			]);
		}

		return redirect()->route('grades_list');
	}

	public function freeze(Request $request, Grade $grade) {

		$grade->freezed = 1;
		$grade->save();

		return redirect()->route('grades_list');
	}

	public function unfreeze(Request $request, Grade $grade) {

		$grade->freezed = 0;
		$grade->save();

		return redirect()->route('grades_list');
	}

	public function delete(Request $request, Grade $grade) {

		// Check delete permissions
		if (!auth()->user()->hasPermissionTo('delete_exgrades')) {
			abort(403, 'Unauthorized to delete grades');
		}

		$grade->delete();

		return redirect()->route('grades_list');
	}

	/**
	 * Create an appeal for a grade
	 */
	public function appeal(Request $request, Grade $grade) {

		// Only students can appeal their own grades
		if (!auth()->user()->hasRole('exstudent') || $grade->user_id !== auth()->id()) {
			abort(403, 'Unauthorized to appeal this grade');
		}

		// Check if there's already a pending appeal
		if ($grade->hasPendingAppeal()) {
			return redirect()->back()->with('error', 'There is already a pending appeal for this grade.');
		}

		$this->validate($request, [
			'reason' => ['required', 'string', 'max:1000']
		]);

		Appeal::create([
			'grade_id' => $grade->id,
			'user_id' => auth()->id(),
			'reason' => $request->reason,
			'status' => 'pending',
			'appealed_at' => now()
		]);

		return redirect()->back()->with('success', 'Appeal submitted successfully.');
	}
}
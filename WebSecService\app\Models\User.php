<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spa<PERSON>\Permission\Traits\HasRoles;
use Lara<PERSON>\Passport\HasApiTokens;

class User extends Authenticatable
{
    use HasRoles;

    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Get the grades for the user.
     */
    public function grades()
    {
        return $this->hasMany(Grade::class);
    }

    /**
     * Get the appeals made by the user.
     */
    public function appeals()
    {
        return $this->hasMany(Appeal::class);
    }

    /**
     * Get the appeals resolved by the user.
     */
    public function resolvedAppeals()
    {
        return $this->hasMany(Appeal::class, 'resolved_by');
    }
}

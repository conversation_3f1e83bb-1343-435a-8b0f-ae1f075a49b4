@extends('layouts.master')
@section('title', 'List Grades')
@section('content')
<div class="row my-4">
    <div class="col col-10">
        <h1>Grades</h1>
    </div>
    <div class="col col-2">
        @can('edit_exgrades')
        <a href="{{route('grades_edit')}}" class="btn btn-success form-control">Add Grade</a>
        @endcan
    </div>
</div>
<form>
    <div class="row mb-4">
        <div class="col col-sm-6">
            <input name="keywords" type="text"  class="form-control" placeholder="Search Keywords" value="{{ request()->keywords }}" />
        </div>
        <div class="col col-sm-2">
            <select name="order_by" class="form-select">
                <option value="" {{ request()->order_by==""?"selected":"" }} disabled>Order By</option>
                <option value="courses.name" {{ request()->order_by=="courses.name"?"selected":"" }}>Course Name</option>
                <option value="users.name" {{ request()->order_by=="users.name"?"selected":"" }}>Student Name</option>
            </select>
        </div>
        <div class="col col-sm-2">
            <select name="order_direction" class="form-select">
                <option value="" {{ request()->order_direction==""?"selected":"" }} disabled>Order Direction</option>
                <option value="ASC" {{ request()->order_direction=="ASC"?"selected":"" }}>ASC</option>
                <option value="DESC" {{ request()->order_direction=="DESC"?"selected":"" }}>DESC</option>
            </select>
        </div>
        <div class="col col-sm-1">
            <button type="submit" class="btn btn-primary">Submit</button>
        </div>
        <div class="col col-sm-1">
            <button type="reset" class="btn btn-danger">Reset</button>
        </div>
    </div>
</form>


<div class="card mt-2">
    <div class="card-body">
        <table class="table">
            <thead>
                <tr>
                    <th scope="col">Student</th>
                    <th scope="col">Course</th>
                    <th scope="col">Grade</th>
                    @if(auth()->user()->hasPermissionTo('show_exgrades'))
                    <th scope="col">Appeal Status</th>
                    @endif
                    <th scope="col">Actions</th>
                </tr>
            </thead>
            @foreach($grades as $grade)
            <tr>
                <td scope="col">{{$grade->user->name}}</td>
                <td scope="col">{{$grade->course->name}}</td>
                <td scope="col">{{$grade->degree}} / {{$grade->course->max_degree}}</td>
                @if(auth()->user()->hasPermissionTo('show_exgrades'))
                <td scope="col">
                    @php
                        $appealStatus = $grade->getAppealStatus();
                        $badgeClass = match($appealStatus) {
                            'pending' => 'bg-warning',
                            'closed' => 'bg-secondary',
                            'approved' => 'bg-success',
                            'rejected' => 'bg-danger',
                            default => 'bg-light text-dark'
                        };
                    @endphp
                    <span class="badge {{$badgeClass}}">
                        {{ucfirst($appealStatus === 'none' ? 'No Appeal' : $appealStatus)}}
                    </span>
                </td>
                @endif
                <td scope="col">
                    <div class="row mb-2">
                        @can('edit_exgrades')
                        <div class="col col-3">
                            <a href="{{route('grades_edit', $grade->id)}}" class="btn btn-success btn-sm form-control">Edit</a>
                        </div>
                        @endcan
                        @can('delete_exgrades')
                        <div class="col col-3">
                            <a href="{{route('grades_delete', $grade->id)}}" class="btn btn-danger btn-sm form-control">Delete</a>
                        </div>
                        @endcan
                        @if(auth()->user()->hasRole('exstudent') && $grade->user_id === auth()->id() && !$grade->hasPendingAppeal())
                        <div class="col col-3">
                            <button type="button" class="btn btn-warning btn-sm form-control" data-bs-toggle="modal" data-bs-target="#appealModal{{$grade->id}}">
                                Appeal
                            </button>
                        </div>
                        @endif
                    </div>
                </td>
            </tr>
            @endforeach
        </table>
    </div>
</div>

<!-- Appeal Modals -->
@foreach($grades as $grade)
@if(auth()->user()->hasRole('exstudent') && $grade->user_id === auth()->id() && !$grade->hasPendingAppeal())
<div class="modal fade" id="appealModal{{$grade->id}}" tabindex="-1" aria-labelledby="appealModalLabel{{$grade->id}}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="appealModalLabel{{$grade->id}}">Appeal Grade</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{route('grades_appeal', $grade->id)}}" method="post">
                {{ csrf_field() }}
                <div class="modal-body">
                    <p><strong>Course:</strong> {{$grade->course->name}}</p>
                    <p><strong>Current Grade:</strong> {{$grade->degree}} / {{$grade->course->max_degree}}</p>
                    <div class="mb-3">
                        <label for="reason{{$grade->id}}" class="form-label">Reason for Appeal:</label>
                        <textarea class="form-control" id="reason{{$grade->id}}" name="reason" rows="4" required placeholder="Please explain why you are appealing this grade..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Submit Appeal</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endif
@endforeach

@if(session('success'))
<div class="alert alert-success alert-dismissible fade show" role="alert">
    {{ session('success') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif

@if(session('error'))
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    {{ session('error') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif

@endsection
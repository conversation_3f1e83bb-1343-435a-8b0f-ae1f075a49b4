<?php $__env->startSection('title', 'List Grades'); ?>
<?php $__env->startSection('content'); ?>
<div class="row my-4">
    <div class="col col-10">
        <h1>Grades</h1>
    </div>
    <div class="col col-2">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit_exgrades')): ?>
        <a href="<?php echo e(route('grades_edit')); ?>" class="btn btn-success form-control">Add Grade</a>
        <?php endif; ?>
    </div>
</div>
<form>
    <div class="row mb-4">
        <div class="col col-sm-6">
            <input name="keywords" type="text"  class="form-control" placeholder="Search Keywords" value="<?php echo e(request()->keywords); ?>" />
        </div>
        <div class="col col-sm-2">
            <select name="order_by" class="form-select">
                <option value="" <?php echo e(request()->order_by==""?"selected":""); ?> disabled>Order By</option>
                <option value="courses.name" <?php echo e(request()->order_by=="courses.name"?"selected":""); ?>>Course Name</option>
                <option value="users.name" <?php echo e(request()->order_by=="users.name"?"selected":""); ?>>Student Name</option>
            </select>
        </div>
        <div class="col col-sm-2">
            <select name="order_direction" class="form-select">
                <option value="" <?php echo e(request()->order_direction==""?"selected":""); ?> disabled>Order Direction</option>
                <option value="ASC" <?php echo e(request()->order_direction=="ASC"?"selected":""); ?>>ASC</option>
                <option value="DESC" <?php echo e(request()->order_direction=="DESC"?"selected":""); ?>>DESC</option>
            </select>
        </div>
        <div class="col col-sm-1">
            <button type="submit" class="btn btn-primary">Submit</button>
        </div>
        <div class="col col-sm-1">
            <button type="reset" class="btn btn-danger">Reset</button>
        </div>
    </div>
</form>


<div class="card mt-2">
    <div class="card-body">
        <table class="table">
            <thead>
                <tr>
                    <th scope="col">Student</th>
                    <th scope="col">Course</th>
                    <th scope="col">Grade</th>
                    <?php if(auth()->user()->hasPermissionTo('show_exgrades')): ?>
                    <th scope="col">Appeal Status</th>
                    <?php endif; ?>
                    <th scope="col">Actions</th>
                </tr>
            </thead>
            <?php $__currentLoopData = $grades; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $grade): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr>
                <td scope="col"><?php echo e($grade->user->name); ?></td>
                <td scope="col"><?php echo e($grade->course->name); ?></td>
                <td scope="col"><?php echo e($grade->degree); ?> / <?php echo e($grade->course->max_degree); ?></td>
                <?php if(auth()->user()->hasPermissionTo('show_exgrades')): ?>
                <td scope="col">
                    <?php
                        $appealStatus = $grade->getAppealStatus();
                        $badgeClass = match($appealStatus) {
                            'pending' => 'bg-warning',
                            'closed' => 'bg-secondary',
                            'approved' => 'bg-success',
                            'rejected' => 'bg-danger',
                            default => 'bg-light text-dark'
                        };
                    ?>
                    <span class="badge <?php echo e($badgeClass); ?>">
                        <?php echo e(ucfirst($appealStatus === 'none' ? 'No Appeal' : $appealStatus)); ?>

                    </span>
                </td>
                <?php endif; ?>
                <td scope="col">
                    <div class="row mb-2">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit_exgrades')): ?>
                        <div class="col col-3">
                            <a href="<?php echo e(route('grades_edit', $grade->id)); ?>" class="btn btn-success btn-sm form-control">Edit</a>
                        </div>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete_exgrades')): ?>
                        <div class="col col-3">
                            <a href="<?php echo e(route('grades_delete', $grade->id)); ?>" class="btn btn-danger btn-sm form-control">Delete</a>
                        </div>
                        <?php endif; ?>
                        <?php if(auth()->user()->hasRole('exstudent') && $grade->user_id === auth()->id() && !$grade->hasPendingAppeal()): ?>
                        <div class="col col-3">
                            <button type="button" class="btn btn-warning btn-sm form-control" data-bs-toggle="modal" data-bs-target="#appealModal<?php echo e($grade->id); ?>">
                                Appeal
                            </button>
                        </div>
                        <?php endif; ?>
                    </div>
                </td>
            </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </table>
    </div>
</div>

<!-- Appeal Modals -->
<?php $__currentLoopData = $grades; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $grade): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
<?php if(auth()->user()->hasRole('exstudent') && $grade->user_id === auth()->id() && !$grade->hasPendingAppeal()): ?>
<div class="modal fade" id="appealModal<?php echo e($grade->id); ?>" tabindex="-1" aria-labelledby="appealModalLabel<?php echo e($grade->id); ?>" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="appealModalLabel<?php echo e($grade->id); ?>">Appeal Grade</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?php echo e(route('grades_appeal', $grade->id)); ?>" method="post">
                <?php echo e(csrf_field()); ?>

                <div class="modal-body">
                    <p><strong>Course:</strong> <?php echo e($grade->course->name); ?></p>
                    <p><strong>Current Grade:</strong> <?php echo e($grade->degree); ?> / <?php echo e($grade->course->max_degree); ?></p>
                    <div class="mb-3">
                        <label for="reason<?php echo e($grade->id); ?>" class="form-label">Reason for Appeal:</label>
                        <textarea class="form-control" id="reason<?php echo e($grade->id); ?>" name="reason" rows="4" required placeholder="Please explain why you are appealing this grade..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Submit Appeal</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

<?php if(session('success')): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <?php echo e(session('success')); ?>

    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<?php if(session('error')): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <?php echo e(session('error')); ?>

    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\websec\WebSecService\resources\views/grades/list.blade.php ENDPATH**/ ?>